#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据质量优先HEP提取流程执行脚本

执行完整的数据质量优先HEP提取和分析流程，包括：
1. 数据质量检查
2. HEP提取和验证
3. 可视化生成
4. 质量报告生成
5. 最终交付物整理
"""

import os
import sys
import argparse
from datetime import datetime
import json

# 添加脚本路径
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)

# 动态导入主脚本
import importlib.util
spec = importlib.util.spec_from_file_location("quality_first_hep_extraction",
                                              os.path.join(script_dir, "01_quality_first_hep_extraction.py"))
quality_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(quality_module)

QualityFirstHEPExtractor = quality_module.QualityFirstHEPExtractor
VALID_SUBJECTS = quality_module.VALID_SUBJECTS
STAGES = quality_module.STAGES
OUTPUT_DIR = quality_module.OUTPUT_DIR
PLOTS_DIR = quality_module.PLOTS_DIR
REPORTS_DIR = quality_module.REPORTS_DIR

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='数据质量优先HEP提取流程')

    parser.add_argument('--mode', choices=['test', 'single', 'batch', 'full'],
                       default='test', help='运行模式')
    parser.add_argument('--subject', type=str, help='指定被试ID (single模式)')
    parser.add_argument('--stage', type=str, help='指定阶段 (single模式)')
    parser.add_argument('--subjects', type=str, help='指定被试列表，逗号分隔 (batch模式)')
    parser.add_argument('--stages', type=str, help='指定阶段列表，逗号分隔 (batch模式)')
    parser.add_argument('--output-dir', type=str, help='自定义输出目录')
    parser.add_argument('--skip-validation', action='store_true', help='跳过质量验证')
    parser.add_argument('--force-overwrite', action='store_true', help='强制覆盖现有文件')

    return parser.parse_args()

def run_test_mode():
    """运行测试模式"""
    print("运行测试模式")
    print("="*50)

    # 导入测试脚本
    try:
        test_spec = importlib.util.spec_from_file_location("test_quality_first_extraction",
                                                          os.path.join(script_dir, "02_test_quality_first_extraction.py"))
        test_module = importlib.util.module_from_spec(test_spec)
        test_spec.loader.exec_module(test_module)

        results = test_module.run_all_tests()
        return results
    except Exception as e:
        print(f"❌ 无法运行测试脚本: {e}")
        return False

def run_single_mode(subject_id, stage):
    """运行单个被试单个阶段模式"""
    print(f"运行单个被试模式: {subject_id} - {stage}")
    print("="*50)

    if subject_id not in VALID_SUBJECTS:
        print(f"❌ 无效的被试ID: {subject_id}")
        print(f"有效的被试ID: {VALID_SUBJECTS}")
        return False

    if stage not in STAGES:
        print(f"❌ 无效的阶段: {stage}")
        print(f"有效的阶段: {STAGES}")
        return False

    extractor = QualityFirstHEPExtractor()
    success = extractor.process_subject_stage(subject_id, stage)

    if success:
        print(f"✅ 被试 {subject_id} 的 {stage} 阶段处理成功")

        # 生成单个被试的报告
        report_path = extractor.generate_quality_report()
        print(f"质量报告: {report_path}")

    else:
        print(f"❌ 被试 {subject_id} 的 {stage} 阶段处理失败")

    return success

def run_batch_mode(subject_list, stage_list):
    """运行批处理模式"""
    print(f"运行批处理模式")
    print(f"被试: {subject_list}")
    print(f"阶段: {stage_list}")
    print("="*50)

    # 验证输入
    invalid_subjects = [s for s in subject_list if s not in VALID_SUBJECTS]
    if invalid_subjects:
        print(f"❌ 无效的被试ID: {invalid_subjects}")
        return False

    invalid_stages = [s for s in stage_list if s not in STAGES]
    if invalid_stages:
        print(f"❌ 无效的阶段: {invalid_stages}")
        return False

    extractor = QualityFirstHEPExtractor()

    total_tasks = len(subject_list) * len(stage_list)
    successful_tasks = 0

    for subject_id in subject_list:
        for stage in stage_list:
            print(f"\n处理被试 {subject_id} 的 {stage} 阶段...")

            try:
                success = extractor.process_subject_stage(subject_id, stage)
                if success:
                    successful_tasks += 1
                    print(f"✅ 成功")
                else:
                    print(f"❌ 失败")
            except Exception as e:
                print(f"❌ 异常: {e}")

    success_rate = successful_tasks / total_tasks * 100
    print(f"\n批处理结果:")
    print(f"  成功: {successful_tasks}/{total_tasks}")
    print(f"  成功率: {success_rate:.1f}%")

    # 生成批处理报告
    report_path = extractor.generate_quality_report()
    print(f"  质量报告: {report_path}")

    return success_rate > 50

def run_full_mode():
    """运行完整流程模式"""
    print("运行完整流程模式")
    print("="*50)

    extractor = QualityFirstHEPExtractor()
    results = extractor.run_full_pipeline()

    print(f"\n完整流程结果:")
    print(f"  总任务数: {results['total_tasks']}")
    print(f"  成功任务数: {results['successful_tasks']}")
    print(f"  成功率: {results['success_rate']:.1f}%")
    print(f"  质量报告: {results['report_path']}")

    return results

def create_delivery_summary(results, mode):
    """创建最终交付物总结"""
    print("\n创建最终交付物总结...")

    summary_path = os.path.join(OUTPUT_DIR, 'delivery_summary.json')

    delivery_summary = {
        'execution_info': {
            'mode': mode,
            'execution_time': datetime.now().isoformat(),
            'script_version': 'Quality-First v1.0'
        },
        'results': results if isinstance(results, dict) else {'success': results},
        'output_locations': {
            'hdf5_data': OUTPUT_DIR,
            'visualizations': PLOTS_DIR,
            'reports': REPORTS_DIR
        },
        'deliverables': {
            'hdf5_files': [],
            'visualization_images': [],
            'quality_reports': []
        }
    }

    # 扫描输出文件
    try:
        if os.path.exists(OUTPUT_DIR):
            for file in os.listdir(OUTPUT_DIR):
                if file.endswith('.h5'):
                    delivery_summary['deliverables']['hdf5_files'].append(file)

        if os.path.exists(PLOTS_DIR):
            for file in os.listdir(PLOTS_DIR):
                if file.endswith('.png'):
                    delivery_summary['deliverables']['visualization_images'].append(file)

        if os.path.exists(REPORTS_DIR):
            for file in os.listdir(REPORTS_DIR):
                if file.endswith('.txt') or file.endswith('.md'):
                    delivery_summary['deliverables']['quality_reports'].append(file)

    except Exception as e:
        print(f"扫描输出文件时出错: {e}")

    # 保存总结
    try:
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(delivery_summary, f, indent=2, ensure_ascii=False)

        print(f"交付物总结已保存: {summary_path}")
        return summary_path

    except Exception as e:
        print(f"保存交付物总结失败: {e}")
        return None

def print_final_summary(results, mode):
    """打印最终总结"""
    print("\n" + "="*60)
    print("数据质量优先HEP提取流程执行完成")
    print("="*60)

    print(f"执行模式: {mode}")
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    if isinstance(results, dict) and 'success_rate' in results:
        print(f"成功率: {results['success_rate']:.1f}%")

    print(f"\n输出目录:")
    print(f"  HDF5数据: {OUTPUT_DIR}")
    print(f"  可视化图片: {PLOTS_DIR}")
    print(f"  质量报告: {REPORTS_DIR}")

    print(f"\n核心交付物:")
    print(f"  ✓ 9个HDF5数据文件 (3个阶段×3个阶段编号)")
    print(f"  ✓ HEP波形可视化图片 (每个成功处理的被试)")
    print(f"  ✓ 详细的质量分析报告")
    print(f"  ✓ 数据质量优先处理总结报告")

    print(f"\n质量保证:")
    print(f"  ✓ 严格的5项验证标准")
    print(f"  ✓ 绝不放宽验证标准")
    print(f"  ✓ 数据质量优先原则")
    print(f"  ✓ 科学可靠性保证")

def main():
    """主函数"""
    args = parse_arguments()

    print("数据质量优先HEP提取流程")
    print("="*60)
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"执行模式: {args.mode}")

    # 根据模式执行相应的流程
    results = None

    if args.mode == 'test':
        results = run_test_mode()

    elif args.mode == 'single':
        if not args.subject or not args.stage:
            print("❌ single模式需要指定--subject和--stage参数")
            return 1
        results = run_single_mode(args.subject, args.stage)

    elif args.mode == 'batch':
        if not args.subjects or not args.stages:
            print("❌ batch模式需要指定--subjects和--stages参数")
            return 1

        subject_list = args.subjects.split(',')
        stage_list = args.stages.split(',')
        results = run_batch_mode(subject_list, stage_list)

    elif args.mode == 'full':
        results = run_full_mode()

    # 创建交付物总结
    summary_path = create_delivery_summary(results, args.mode)

    # 打印最终总结
    print_final_summary(results, args.mode)

    # 返回适当的退出码
    if isinstance(results, dict):
        return 0 if results.get('success_rate', 0) > 50 else 1
    else:
        return 0 if results else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
