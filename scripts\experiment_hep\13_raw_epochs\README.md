# 数据质量优先的HEP提取和分析流程

## 概述

本流程基于严格的数据质量控制标准，实施HEP（心跳诱发电位）数据提取、验证和分析的完整流程。遵循数据质量优先原则：通过改善预处理技术解决质量问题，绝不放宽验证标准。

## 核心特性

### 技术规范
- **信号增强**：V→μV转换（×1,000,000倍）
- **滤波参数**：0.1-30Hz自适应带通滤波
- **基线校正**：-200ms到0ms统一窗口
- **R波检测**：多算法优化，±5采样点精度
- **伪迹检测**：多层次质量控制

### 严格验证标准
1. **时间窗口**：-200ms到+650ms可视化
2. **子图比例**：2:1横纵比
3. **R波对齐**：0ms位置精确对齐（±10ms容差）
4. **HEP质量**：基线<30μV，HEP变化≥2.0倍基线
5. **R波可见性**：0ms位置最大幅值（±5采样点）

### 输出组织
- **HDF5数据**：4个电极组（全通道、中央、左右半球）
- **可视化**：24×4英寸，2:1比例子图
- **质量报告**：详细处理和验证记录

## 文件结构

```
scripts/experiment_hep/13_raw_epochs/
├── 01_quality_first_hep_extraction.py    # 主要提取脚本
├── 02_test_quality_first_extraction.py   # 测试脚本
├── 03_cleanup_old_scripts.py             # 清理旧脚本
├── 04_run_quality_first_pipeline.py      # 执行脚本
└── README.md                              # 说明文档
```

## 使用方法

### 1. 测试模式
```bash
python 04_run_quality_first_pipeline.py --mode test
```
运行所有测试，验证脚本功能。

### 2. 单个被试模式
```bash
python 04_run_quality_first_pipeline.py --mode single --subject 01 --stage prac
```
处理指定被试的指定阶段。

### 3. 批处理模式
```bash
python 04_run_quality_first_pipeline.py --mode batch --subjects "01,02,05" --stages "prac,test1,rest1"
```
处理指定被试列表的指定阶段列表。

### 4. 完整流程模式
```bash
python 04_run_quality_first_pipeline.py --mode full
```
处理所有29个有效被试的所有7个阶段。

## 输出文件

### HDF5数据文件
每个阶段生成一个HDF5文件，包含：
- `all_channels_hep`：全部119通道数据
- `central_electrodes_hep`：中央电极组
- `right_hemisphere_hep`：右半球电极组
- `left_hemisphere_hep`：左半球电极组

### 可视化图片
每个成功处理的被试生成一个PNG图片：
- 格式：`{subject_ID:02d}_{stage_type}_{stage_number}_hep_average.png`
- 内容：三个电极组的HEP平均波形
- 规格：24×4英寸，2:1比例子图

### 质量报告
- 详细的处理日志
- 质量指标统计
- 失败案例分析
- 验证结果总结

## 质量控制

### 数据质量优先原则
1. **严格标准**：绝不放宽验证标准
2. **技术改进**：通过改善预处理技术解决质量问题
3. **科学可靠**：确保最终数据集的科学可靠性
4. **生理合理**：保证生理学合理性

### 多层次验证
1. **R波检测质量**：多算法验证
2. **伪迹去除效果**：严格阈值控制
3. **基线稳定性**：<30μV标准
4. **HEP组件清晰度**：≥2.0倍基线变化
5. **时间对齐精度**：±5采样点容差

## 被试信息

- **总被试数**：32个
- **有效被试**：29个（排除03、04、14号）
- **处理阶段**：7个（prac, rest1, test1, rest2, test2, rest3, test3）
- **总任务数**：203个（29×7）

## 电极组定义

### 中央电极组
F1, F2, FC1, FC2, C1, Cz, C2, Fz

### 右半球电极组
F2, F4, F6, AF4, AF8, FP2, FC2, FC4, FC6

### 左半球电极组
F1, F3, F5, AF3, AF7, FP1, FC1, FC3, FC5

## 技术要求

### 依赖包
- numpy
- pandas
- matplotlib
- mne
- neurokit2
- scipy
- scikit-learn
- h5py
- tqdm

### 系统要求
- Python 3.7+
- 内存：建议16GB+
- 存储：建议10GB+可用空间

## 故障排除

### 常见问题
1. **找不到数据文件**：检查RAW_DATA_DIR路径设置
2. **内存不足**：减少并行处理的被试数量
3. **R波检测失败**：检查ECG通道质量
4. **质量验证失败**：查看详细的验证报告

### 调试模式
使用测试模式进行调试：
```bash
python 04_run_quality_first_pipeline.py --mode test
```

## 版本信息

- **版本**：Quality-First v1.0
- **作者**：HEP Analysis Team
- **日期**：2024年
- **许可**：内部使用

## 联系方式

如有问题或建议，请联系HEP分析团队。
